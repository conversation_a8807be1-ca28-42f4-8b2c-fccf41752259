return {
    -- 括號自動補齊
    {
        "windwp/nvim-autopairs",
        cond = not vim.g.vscode,
        event = "InsertEnter",
        opts = {
            check_ts = true, -- 啟用treesitter
        },

        config = function ()
            require("nvim-autopairs").setup(opts)
            -- 搭配nvim-cmp
            local cmp_autopairs = require("nvim-autopairs.completion.cmp")
            local cmp = require("cmp")
            cmp.event:on("confirm_done", cmp_autopairs.on_confirm_done())
        end
    }
}
