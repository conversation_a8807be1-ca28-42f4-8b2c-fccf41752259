return {
  -- mason 安裝器
  {
    "williamboman/mason.nvim",
    cond = not vim.g.vscode,
    config = function()
      require("mason").setup()
    end,
  },

  -- mason + 自動安裝 LSP + 自動初始化 lspconfig
  {
    "williamboman/mason-lspconfig.nvim",
    cond = not vim.g.vscode,
    opts = {
      ensure_installed = {
        "eslint",
        "gopls",
        "lua_ls",
      },
      handlers = {
        volar = function() end, -- 禁用 volar 自動註冊
        function(server_name)
          require("lspconfig")[server_name].setup({})
        end,
      },
    },
  },

  -- LSP 自定義設定
  {
    "neovim/nvim-lspconfig",
    cond = not vim.g.vscode,
    config = function()
      local lspconfig = require("lspconfig")
      local util = require("lspconfig.util")

      -- ✅ ESLint 自動修正 on save
      lspconfig.eslint.setup({
        on_attach = function(_, bufnr)
          vim.api.nvim_create_autocmd("BufWritePre", {
            buffer = bufnr,
            callback = function()
              vim.cmd("EslintFixAll")
            end,
          })
        end,
        settings = {
          workingDirectories = { mode = "auto" },
        },
      })

      -- ✅ 自動啟用 volar（在 vue 專案）
      local root_dir = util.root_pattern("package.json", "tsconfig.json", ".git")(vim.fn.getcwd())
      if not root_dir then return end

      local tsdk_path = vim.fs.find("node_modules/typescript/lib", {
        upward = true,
        path = root_dir,
        type = "directory",
      })[1]

      if not tsdk_path then
        vim.notify("⚠️ 找不到 node_modules/typescript/lib，自動跳過 volar", vim.log.levels.WARN)
        return
      end

      lspconfig.volar.setup({
        root_dir = root_dir,
        filetypes = {
          "typescript", "javascript", "javascriptreact",
          "typescriptreact", "vue", "json"
        },
        init_options = {
          typescript = { tsdk = tsdk_path },
          vue = { hybridMode = false },
        },
      })
    end,
  },

  -- Golang 支援
  {
    "ray-x/go.nvim",
    cond = not vim.g.vscode,
    dependencies = {
      "ray-x/guihua.lua",
      "neovim/nvim-lspconfig",
      "nvim-treesitter/nvim-treesitter",
    },
    config = function()
      require("go").setup()
      local format_sync_grp = vim.api.nvim_create_augroup("GoImport", {})
      vim.api.nvim_create_autocmd("BufWritePre", {
        pattern = "*.go",
        callback = function()
          require("go.format").goimport()
        end,
        group = format_sync_grp,
      })
    end,
    event = { "CmdlineEnter" },
    ft = { "go", "gomod" },
    build = ':lua require("go.install").update_all_sync()',
    keys = {
      { "<localleader>r", "<cmd>GoRun<cr>", desc = "Go Run" },
      { "<localleader>t", "<cmd>GoTest<cr>", desc = "Go Test" },
      { "<leader>gat", "<cmd>GoAddTag<cr>", desc = "Add json tags" },
      { "<leader>gam", "<cmd>GoAddTag mapstructure<cr>", desc = "Add mapstructure tags" },
      { "<leader>gae", "<cmd>GoAddTag env<cr>", desc = "Add env tags" },
    },
  },
}
