-- 分頁

return {
    {
        'akinsho/bufferline.nvim',
        version = "*",
        cond = not vim.g.vscode,
        dependencies = 'nvim-tree/nvim-web-devicons',
        config = function()
            require("bufferline").setup({
                options = {
                    numbers = "ordinal", -- 顯示編號 
                    mode = "buffers",
                    highlight = {
                        buffer_selected = {
                            fg = "#ffffff",  -- 当前分頁字体颜色
                            bg = "#5e81ac",  -- 当前分頁背景颜色（选择一个明亮的颜色）
                            bold = true,     -- 加粗字体
                        },
                        buffer_visible = {
                            fg = "#a3be8c",  -- 其他分頁的字体颜色
                            bg = "#3b4252",  -- 其他分頁的背景颜色
                        },
                        separator = {
                            fg = "#81a1c1",  -- 分隔线颜色
                            bg = "#3b4252",  -- 分隔线的背景颜色
                        },
                    },
                    close_command = "bdelete! %d", -- 关闭缓冲区的命令
                    right_mouse_command = "bdelete! %d", -- 
                    indicator_icon = "▎",
                    diagnostics = "nvim_lsp", -- 显示 LSP 诊断信息
                    separator_style = "thin", -- 分頁分隔的風格
                    show_buffer_close_icons = false, -- 顯示關閉的icon
                    show_close_icon = false, -- 不显示全局关闭按钮
                    persist_buffer_sort = true, -- 保持缓冲区排序
                    always_show_bufferline = true, -- 始终显示 bufferline
                    enforce_regular_tabs = false
                },
            })
        end

    }
}
