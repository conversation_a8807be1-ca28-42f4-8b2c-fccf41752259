return {
    {
        "nvim-treesitter/nvim-treesitter",
        build = ":TSUpdate",
        config = function ()
            local config = require("nvim-treesitter.configs")
            config.setup({
                ensure_installed = {
                    "lua",        -- <PERSON>a
                    "go",         -- Go
                    "html",       -- HTML
                    "typescript", -- TypeScript
                    "javascript", -- JavaScript
                    "vue",        -- Vue
                    "css",        -- CSS
                    "json",        -- JSON
                },
                auto_install = true,
                highhlight = {enable = true},
                indent = {enable = true},
            })
        end
    }
}
