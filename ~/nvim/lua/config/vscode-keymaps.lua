-- VSCode Neovim 專用快捷鍵設定

local function map(mode, lhs, rhs, opts)
    opts = opts or {}
    opts.silent = true
    vim.keymap.set(mode, lhs, rhs, opts)
end

-- 視窗導航
map("n", "<C-h>", "<CMD>call VSCodeNotify('workbench.action.navigateLeft')<CR>")
map("n", "<C-l>", "<CMD>call VSCodeNotify('workbench.action.navigateRight')<CR>")
map("n", "<C-k>", "<CMD>call VSCodeNotify('workbench.action.navigateUp')<CR>")
map("n", "<C-j>", "<CMD>call VSCodeNotify('workbench.action.navigateDown')<CR>")

-- 檔案導航
map("n", "<leader><leader>", "<CMD>call VSCodeNotify('workbench.action.quickOpen')<CR>", { desc = "快速開啟檔案" })
map("n", "<leader>/", "<CMD>call VSCodeNotify('workbench.action.findInFiles')<CR>", { desc = "在檔案中搜尋" })

-- 檔案瀏覽
map("n", "<leader>e", "<CMD>call VSCodeNotify('workbench.action.toggleSidebarVisibility')<CR>", { desc = "切換側邊欄" })

-- 緩衝區操作
map("n", "<leader>d", "<CMD>call VSCodeNotify('workbench.action.closeActiveEditor')<CR>", { desc = "關閉當前編輯器" })

-- LSP 功能
map("n", "gd", "<CMD>call VSCodeNotify('editor.action.revealDefinition')<CR>", { desc = "前往定義" })
map("n", "gr", "<CMD>call VSCodeNotify('editor.action.goToReferences')<CR>", { desc = "查看引用" })
map("n", "gI", "<CMD>call VSCodeNotify('editor.action.goToImplementation')<CR>", { desc = "前往實現" })
map("n", "K", "<CMD>call VSCodeNotify('editor.action.showHover')<CR>", { desc = "顯示提示" })

-- Harpoon 整合 (需要安裝 VSCode Harpoon 擴展)
map("n", "<leader>a", "<CMD>call VSCodeNotify('vscode-harpoon.addEditor')<CR>", { desc = "添加到 Harpoon" })
map("n", "<leader>h", "<CMD>call VSCodeNotify('vscode-harpoon.editorQuickPick')<CR>", { desc = "Harpoon 快速選擇" })

-- 為 Harpoon 數字快捷鍵
for i = 1, 9 do
    map("n", "<leader>" .. i, "<CMD>call VSCodeNotify('vscode-harpoon.gotoEditor" .. i .. "')<CR>", 
        { desc = "Harpoon 檔案 " .. i })
end
