-- vim 設定
vim.g.mapleader = " "
vim.scriptencoding = "utf-9"
vim.opt.encoding = "utf-8"
vim.opt.fileencoding = "utf-8"
vim.opt.number = true
vim.opt.title = true
vim.opt.autoindent = true
vim.opt.smartindent = true
vim.opt.hlsearch = true
vim.opt.backup = false

vim.opt.shiftwidth = 4 -- Size of an indent
vim.opt.tabstop = 4
vim.opt.wrap = true
vim.opt.relativenumber = true -- 顯示相對行號
vim.opt.mouse = "a"
vim.opt.expandtab = true -- Use spaces instead of tabs

--復制貼上用的, 按v選取後按p(復制)，可以貼到任何地方 
vim.opt.clipboard = "unnamedplus"

-- -- 確保啟動時程式碼colorscheme可以生效.
-- vim.api.nvim_create_autocmd("BufEnter", {
--   pattern = "*",
--   callback = function()
--     vim.cmd("TSBufEnable highlight")
--   end,
-- })
