-- 快捷鍵設定--

local function map(mode, lhs, rhs, opts)
    opts = opts or {}
    opts.silent = true
    vim.keymap.set(mode, lhs, rhs, opts)
end

-- 只在非 VSCode 環境中設定 Neovim 特有的快捷鍵
if not vim.g.vscode then
    -- Explorer
    map("n", "<leader>w", "<CMD>Explore<CR>", { desc = "開啟 Explore" })

    -- NeoTree
    map("n", "<leader>be", "<CMD>Neotree buffers toggle<CR>", { desc = "開啟NeoTree" })
    map("n", "<leader>e", "<CMD>Neotree filesystem toggle left<CR>", { desc = "Explorer NeoTree (Root Dir)" })
    map("n", "<leader>E", "<CMD>Neotree filesystem toggle current<CR>", { desc = "Explorer NeoTree (cwd)" })
    map("n", "<leader>fe", "<CMD>Neotree filesystem reveal<CR>", { desc = "Explorer NeoTree (Root Dir)" })
    map("n", "<leader>fE", "<CMD>Neotree filesystem reveal current<CR>", { desc = "Explorer NeoTree (cwd)" })
    map("n", "<leader>ge", "<CMD>Neotree git_status toggle<CR>", { desc = "Git 狀態開啟" })
    
    -- Buffer 操作
    map("n", "<leader>d", ":bp|bd #<CR>", { desc = "刪除buffer!!!" })
    
    -- Buffer Move
    map("n", "<S-h>", "<CMD>BufferLineCyclePrev<CR>")
    map("n", "<S-l>", "<CMD>BufferLineCycleNext<CR>")
    
    -- LSP 導航 (FzfLua)
    map("n", "gd", "<CMD>FzfLua lsp_definitions jump_to_single_result=true ignore_current_line=true<CR>", {})
    map("n", "gr", "<CMD>FzfLua lsp_references jump_to_single_result=true ignore_current_line=true<CR>", {})
    map("n", "gI", "<CMD>FzfLua lsp_implementations jump_to_single_result=true ignore_current_line=true<CR>", {})
    map("n", "gy", "<CMD>FzfLua lsp_typedefs jump_to_single_result=true ignore_current_line=true<CR>", {})
end

-- 在 VSCode 和 Neovim 中都可用的快捷鍵
-- Window Navigation
map("n", "<C-h>", "<C-w>h")
map("n", "<C-l>", "<C-w>l")
map("n", "<C-k>", "<C-w>k")
map("n", "<C-j>", "<C-w>j")
