-- return {
--     {
--         "rebelot/kanagawa.nvim",
--         config = function()
--             require('kanagawa').setup({
--                 compile = false,             -- enable compiling the colorscheme
--                 undercurl = true,            -- enable undercurls
--                 commentStyle = { italic = true },
--                 functionStyle = {},
--                 keywordStyle = { italic = true},
--                 statementStyle = { bold = true },
--                 typeStyle = {},
--                 transparent = false,         -- do not set background color
--                 dimInactive = false,         -- dim inactive window `:h hl-NormalNC`
--                 terminalColors = true,       -- define vim.g.terminal_color_{0,17}
--                 colors = {                   -- add/modify theme and palette colors
--                     palette = {},
--                     theme = { wave = {}, lotus = {}, dragon = {}, all = {} },
--                 },
--                 overrides = function(colors) -- add/modify highlights
--                     return {}
--                 end,
--                 theme = "lotus",              -- Load "wave" theme when 'background' option is not set
--                 background = {               -- map the value of 'background' option to a theme
--                     dark = "dragon",           -- try "dragon" !
--                     light = "lotus"
--                 },
--             })
--
--             -- setup must be called before loading
--             vim.cmd("colorscheme kanagawa")
--         end,
--     }
-- }
return {
    "folke/tokyonight.nvim",
    lazy = false,
    priority = 1000,
    config = function()
        require("tokyonight").setup({
            style = "night", -- 可选："storm", "night", "day", "moon"
            transparent = false, -- 是否使用透明背景
            terminal_colors = true, -- 支持终端颜色
            styles = {
                sidebars = "dark", -- Neo-tree 等侧边栏背景
                floats = "dark", -- 浮动窗口背景
            },
        })
        --vim.cmd([[colorscheme tokyonight]]) -- 启用配色方案
    end,
}
-- return { 
--     "catppuccin/nvim", 
--     name = "catppuccin", 
--     priority = 1000,
--     config = function ()
--         require("catppuccin").setup()
--     end
-- }
-- lua/plugins/rose-pine.lua
-- return {
--     "rose-pine/neovim",
--     name = "rose-pine",
--     priority = 1000,
--     config = function()
--         enable = {
--             terminal = true,
--             legacy_highlights = true, -- Improve compatibility for previous versions of Neovim
--             migrations = true, -- Handle deprecated options automatically
--         },
--         -- vim.cmd("colorscheme rose-pine-main")
--         vim.cmd("colorscheme rose-pine-moon")
--         -- vim.cmd("colorscheme rose-pine-dawn")
--     end
-- }
