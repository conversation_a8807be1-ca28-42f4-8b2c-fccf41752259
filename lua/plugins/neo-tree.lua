return {
    {
        "nvim-lua/plenary.nvim",
        "nvim-tree/nvim-web-devicons", -- not strictly required, but recommended
        "MunifTanjim/nui.nvim"

    },
    {
        "nvim-neo-tree/neo-tree.nvim",
        branch = "v3.x",
        cond = not vim.g.vscode,
        dependencies = {
            "nvim-lua/plenary.nvim",
            "nvim-tree/nvim-web-devicons",
            "MunifTanjim/nui.nvim",
        },
        config = function()
            require("neo-tree").setup({
                close_if_last_window = true,
                filesystem = {
                    follow_current_file = true,
                    hijack_netrw = true,
                    hijack_netrw_behavior = "open_default",
                    filtered_items = {
                        hide_dotfiles = false,
                        hide_gitignored = true,
                    },
                },
                source_selector = {
                    --:winbar = true,
                    statusline = true,
                },
                window = {
                    width = 30,
                    mappings = {
                        ["<space>"] = "toggle_node",
                        ["<cr>"] = "open",
                        ["<esc>"] = "close_window",
                    },
                    border = "rounded", -- 或 "single", "double" 等风格
                },
            })
        end
    }
}
