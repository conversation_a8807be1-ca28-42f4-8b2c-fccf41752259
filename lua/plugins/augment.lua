return {
    {
        "augmentcode/augment.vim",
        event = "VeryLazy",
        config = function()
            -- Augment 基本設定
            vim.g.augment_node_path = "/Users/<USER>/.nvm-fish/v22.3.0/bin/node"
            vim.g.augment_workspace_folders = {
                vim.fn.expand("/Users/<USER>/project"),
                vim.fn.expand("/Users/<USER>/.config/nvim")
            }
            vim.g.augment_disable_completions = false
            -- 設定 Ctrl+Tab 接受 AI 建議
            vim.keymap.set("i", "<C-Tab>", function()
                if vim.fn.pumvisible() == 1 then
                    return "<C-y>"  -- 接受當前選中的建議
                else
                    return "<C-Tab>"  -- 否則保持原功能
                end
            end, { expr = true })

            -- 推薦的快捷鍵（可自行調整）
            vim.keymap.set("n", "<leader>ac", ":Augment chat<CR>", { desc = "Augment Chat" })
            vim.keymap.set("n", "<leader>an", ":Augment chat-new<CR>", { desc = "New Augment Chat" })
            vim.keymap.set("n", "<leader>at", ":Augment chat-toggle<CR>", { desc = "Toggle Augment Chat" })
            vim.keymap.set("n", "<leader>as", ":Augment status<CR>", { desc = "Augment Status" })
            vim.defer_fn(function()
                local output = vim.fn.system("augment status")
                if output:find("Not signed in") then
                    vim.notify("🧠 Augment 尚未登入，請執行 :Augment signin", vim.log.levels.WARN, {
                        title = "Augment Login",
                    })
                end
            end, 1000) -- 延遲 1 秒檢查，避免 UI 還沒初始化好
        end,
    },
}
