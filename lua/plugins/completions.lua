return {
    {
        "hrsh7th/nvim-cmp",
        cond = not vim.g.vscode,
        dependencies = {
            "hrsh7th/cmp-nvim-lsp",    -- LSP 源
            "hrsh7th/cmp-buffer",      -- 缓冲区补全
            "hrsh7th/cmp-path",        -- 文件路径补全
            "hrsh7th/cmp-cmdline",     -- 命令行补全
            "saadparwaiz1/cmp_luasnip",-- LuaSnip 补全
            "L3MON4D3/LuaSnip",        -- 片段引擎
            "rafamadriz/friendly-snippets", -- 常见代码片段库
            "onsails/lspkind.nvim"
        },
        config = function()
            local cmp = require("cmp")
            local luasnip = require("luasnip")
            local lspkind = require("lspkind")

            -- 加载代码片段
            require("luasnip.loaders.from_vscode").lazy_load()
            local auto_select = true

            cmp.setup({
                snippet = {
                    expand = function(args)
                        luasnip.lsp_expand(args.body)
                    end,
                },
                windows = {
                    completion = cmp.config.window.bordered(),
                    documentation = cmp.config.window.bordered(),
                },
                mapping = cmp.mapping.preset.insert({
                    ["<C-Space>"] = cmp.mapping.complete(),
                    ["<CR>"] = cmp.mapping.confirm({ select = true }),
                    ["<Tab>"] = cmp.mapping(function(fallback)
                        if cmp.visible() then
                            cmp.select_next_item()
                        elseif luasnip.expand_or_jumpable() then
                            luasnip.expand_or_jump()
                        else
                            fallback()
                        end
                    end, { "i", "s" }),
                    ["<S-Tab>"] = cmp.mapping(function(fallback)
                        if cmp.visible() then
                            cmp.select_prev_item()
                        elseif luasnip.jumpable(-1) then
                            luasnip.jump(-1)
                        else
                            fallback()
                        end
                    end, { "i", "s" }),
                }),
                sources = cmp.config.sources({
                    { name = "nvim_lsp" }, -- LSP 源
                    { name = "luasnip" },  -- 代码片段源
                }, {
                        { name = "buffer" },   -- 缓冲区源
                        { name = "path" },     -- 文件路径源
                    }),
            })

            -- 为 `/` 提供命令行补全
            cmp.setup.cmdline("/", {
                mapping = cmp.mapping.preset.cmdline(),
                sources = {
                    { name = "buffer" },
                },
            })

            -- 为 `:` 提供路径补全
            cmp.setup.cmdline(":", {
                mapping = cmp.mapping.preset.cmdline(),
                sources = cmp.config.sources({
                    { name = "path" },
                }, {
                        { name = "cmdline" },
                    }),
            })
        end,
    },
}

