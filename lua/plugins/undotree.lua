return {
    "jiaoshijie/undotree",
    cond = not vim.g.vscode,
    dependencies = "nvim-lua/plenary.nvim",
    --config = true,
    config = function ()
        local undotree = require('undotree')

        undotree.setup({
            float_diff = true,  -- using float window previews diff, set this `true` will disable layout option
            layout = "left_left_bottom", -- "left_bottom", "left_left_bottom"
            position = "right", -- "right", "bottom"
            ignore_filetype = { 'undotree', 'undotreeDiff', 'qf', 'TelescopePrompt', 'spectre_panel', 'tsplayground' },
            window = {
                winblend = 30,
            },
        })
    end,
    keys = { -- load the plugin only when using it's keybinding:
        { "<leader>u", "<cmd>lua require('undotree').toggle()<cr>" },
    },
}
