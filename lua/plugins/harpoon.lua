return {
    "ThePrimeagen/harpoon",
    branch = "harpoon2",
    vscode = true, -- 在 VSCode 中啟用
    opts = {
        menu = {
            width = vim.api.nvim_win_get_width(0) - 4,
        },
        settings = {
            save_on_toggle = true,
        },
    },
    keys = function()
        -- 在 VSCode 中使用不同的按鍵映射
        if vim.g.vscode then
            local keys = {
                {
                    "<leader>a",
                    function()
                        vim.fn.VSCodeNotify('vscode-harpoon.addEditor')
                    end,
                    desc = "將當前開啟的檔案到Harpoon File",
                },
                {
                    "<leader>h",
                    function()
                        vim.fn.VSCodeNotify('vscode-harpoon.editEditors')
                    end,
                    desc = "開啟Harpoon Quick Menu",
                },
            }

            for i = 1, 9 do
                table.insert(keys, {
                    "<leader>" .. i,
                    function()
                        vim.fn.VSCodeNotify('vscode-harpoon.gotoEditor' .. i)
                    end,
                    desc = "Harpoon 檔案編號:  " .. i,
                })
            end
            return keys
        else
            -- 原始 Neovim 按鍵映射
            local keys = {
                {
                    "<leader>a",
                    function()
                        require("harpoon"):list():add()
                    end,
                    desc = "將當前開啟的檔案到Harpoon File",
                },
                {
                    "<leader>h",
                    function()
                        local harpoon = require("harpoon")
                        harpoon.ui:toggle_quick_menu(harpoon:list())
                    end,
                    desc = "開啟Harpoon Quick Menu",
                },
            }

            for i = 1, 9 do
                table.insert(keys, {
                    "<leader>" .. i,
                    function()
                        require("harpoon"):list():select(i)
                    end,
                    desc = "Harpoon 檔案編號:  " .. i,
                })
            end
            return keys
        end
    end,
}
