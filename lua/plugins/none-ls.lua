return {
  "nvimtools/none-ls.nvim",
  cond = not vim.g.vscode,
  dependencies = {
    "nvim-lua/plenary.nvim",
    "nvimtools/none-ls-extras.nvim", -- ✅ 這是重點
  },
  event = { "BufReadPre", "BufNewFile" },
  config = function()
    local null_ls = require("null-ls")

    -- ⬇️ 載入 extras 套件的 eslint
    local extras = require("none-ls.diagnostics.eslint")

    null_ls.setup({
      sources = {
        extras.with({
          command = "./node_modules/.bin/eslint",
          cwd = function(params)
            return vim.fn.getcwd()
          end,
          filetypes = { "javascript", "typescript", "vue" },
        }),
      },
      on_attach = function(client, bufnr)
        if client.supports_method("textDocument/formatting") then
          vim.api.nvim_create_autocmd("BufWritePre", {
            group = vim.api.nvim_create_augroup("LspFormatting", {}),
            buffer = bufnr,
            callback = function()
              vim.lsp.buf.format({ bufnr = bufnr })
            end,
          })
        end
      end,
    })
  end,
}
